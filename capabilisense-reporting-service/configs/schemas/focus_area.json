{"type": "object", "properties": {"focus_title": {"type": "string", "description": "AI-generated title for the focus area"}, "focus_description": {"type": "string", "description": "AI-generated description of the focus area"}, "key_recommendations": {"type": "array", "items": {"type": "string"}, "minItems": 3, "maxItems": 5, "description": "AI-generated recommendations for this focus area"}, "implementation_plan": {"type": "string", "description": "AI-generated implementation approach for the focus area"}, "expected_outcomes": {"type": "array", "items": {"type": "string"}, "minItems": 3, "maxItems": 5, "description": "AI-generated expected outcomes from implementing this focus area"}, "notes": {"type": "string", "description": "Any additional analysis, reasoning, or meta-commentary about the focus area generation. Use this field for any explanatory text or processing notes."}}, "required": ["focus_title", "focus_description", "key_recommendations", "implementation_plan", "expected_outcomes", "notes"]}