{"type": "object", "properties": {"organization_name": {"type": "object", "properties": {"generated_name": {"type": "string", "description": "Professional organization name based on assessment context"}, "reasoning": {"type": "string", "description": "Brief explanation of why this name was chosen based on the framework and assessment data"}}, "required": ["generated_name", "reasoning"]}, "business_summary": {"type": "object", "properties": {"executive_summary": {"type": "string", "description": "A concise 2-3 sentence executive summary for one-pager report. Maximum 150 words. Focus on overall maturity level and top priority."}, "key_findings": {"type": "array", "items": {"type": "string"}, "minItems": 2, "maxItems": 3, "description": "List of 2-3 most critical findings. Each finding should be 1 sentence, maximum 20 words."}, "strategic_priorities": {"type": "array", "items": {"type": "string"}, "minItems": 2, "maxItems": 3, "description": "List of 2-3 strategic priorities. Each priority should be 1 sentence, maximum 15 words."}}, "required": ["executive_summary", "key_findings", "strategic_priorities"]}, "notes": {"type": "string", "description": "Any additional analysis, reasoning, or meta-commentary about the business overview generation. Use this field for any explanatory text or processing notes."}}, "required": ["organization_name", "business_summary", "notes"]}