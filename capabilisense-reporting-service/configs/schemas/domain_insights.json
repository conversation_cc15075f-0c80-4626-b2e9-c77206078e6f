{"type": "object", "properties": {"domain_name": {"type": "string", "description": "The exact name of the domain being analyzed"}, "current_state": {"type": "string", "description": "A concise, professional assessment of the current state of this domain based on the evidence and scores provided. Focus on business impact and operational reality."}, "key_strengths": {"type": "array", "items": {"type": "string"}, "minItems": 2, "maxItems": 4, "description": "List of 2-4 specific strengths identified in this domain based on actual capabilities and scores"}, "improvement_areas": {"type": "array", "items": {"type": "string"}, "minItems": 2, "maxItems": 4, "description": "List of 2-4 specific areas needing improvement in this domain based on low scores or gaps"}, "recommendations": {"type": "array", "items": {"type": "string"}, "minItems": 3, "maxItems": 5, "description": "List of 3-5 specific, actionable recommendations for this domain that address the identified gaps"}, "next_steps": {"type": "array", "items": {"type": "string"}, "minItems": 2, "maxItems": 3, "description": "List of 2-3 immediate, concrete next steps to begin improvement in this domain"}, "notes": {"type": "string", "description": "Any additional analysis, reasoning, or meta-commentary about the domain assessment. Use this field for any explanatory text or processing notes."}}, "required": ["domain_name", "current_state", "key_strengths", "improvement_areas", "recommendations", "next_steps", "notes"]}