# CapabiliSense Reporting Service - Debug Guide

## 🔍 Debug Mode Overview

The CapabiliSense Reporting Service includes comprehensive debugging capabilities to help diagnose issues, monitor performance, and analyze AI-generated content.

## 🚀 Quick Start - Enable Debug Mode

```bash
# Enable debug mode
DEBUG=true go run cmd/combined_api/main.go

# You should see debug output like:
# [DEBUG] 🕐 LLM HTTP client timeout set to: 10m0s
# [DEBUG] 🤖 Starting LLM call to gcp-gemini-2.5-pro...
```

## 📊 Debug Features

### 1. **LLM Call Logging**
All LLM interactions are logged to `logs/llm-calls.jsonl` in structured JSON format.

**Log Entry Example:**
```json
{
  "timestamp": "2025-05-25T14:20:18Z",
  "event_type": "llm_request",
  "provider_alias": "gcp-gemini-2.5-pro",
  "model_name": "gemini-2.5-pro-preview-05-06",
  "request_id": "business-summary-123",
  "prompt_id": "business_summary",
  "tokens_used": 15420,
  "processing_time": "12.5s"
}
```

### 2. **Debug Artifacts**
When debug mode is enabled, all input/output data is automatically saved:

```
logs/debug/
├── stage_a_input_ai_20250525_142018.json     # Stage A input data
├── stage_b_output_ai_20250525_142258.json    # Stage B AI insights
└── complete_pdf_ai_20250525_142258.pdf       # Generated PDF
```

### 3. **Performance Metrics**
Real-time performance tracking:
- Token usage per LLM call
- Processing time for each stage
- Overall pipeline performance
- Success/failure rates

### 4. **API Key Security**
- API keys are automatically masked in debug output
- URLs show `key=[REDACTED]` instead of actual keys
- Logs are safe to share for debugging

## 🛠️ Troubleshooting Common Issues

### Issue 1: LLM Timeout Errors

**Symptoms:**
```
❌ HTTP request failed: context deadline exceeded
🕐 This appears to be a timeout error
```

**Solution:**
- ✅ **Already Fixed**: Timeout increased to 10 minutes
- Check LLM provider status if timeouts persist
- Review `logs/llm-calls.jsonl` for specific error details

**Debug Commands:**
```bash
# Check recent LLM call logs
tail -10 logs/llm-calls.jsonl | jq '.event_type, .processing_time, .tokens_used'

# Look for timeout patterns
grep "timeout\|deadline" logs/llm-calls.jsonl
```

### Issue 2: Poor AI Output Quality

**Symptoms:**
- Business summary contains meta-commentary
- Domain insights describe data instead of providing analysis
- Generic placeholder content

**Current Status:**
- ✅ **AI Spotlight**: Fixed
- ✅ **Focus Area**: Fixed  
- ❌ **Business Summary**: Still needs fixing
- ❌ **Domain Insights**: Still needs fixing

**Debug Commands:**
```bash
# Check latest Stage B output
ls -la logs/debug/stage_b_output_*.json | tail -1
cat logs/debug/stage_b_output_ai_*.json | jq '.business_summary.executive_summary'

# Review specific prompt responses
grep "business_summary" logs/llm-calls.jsonl | tail -1 | jq '.response_body'
```

### Issue 3: Stage B API 400 Errors

**Symptoms:**
```
POST /api/v1/generate-insights 400 1.505292ms
```

**Cause:**
Data format mismatch between Stage A output and Stage B input expectations.

**Workaround:**
Use the complete pipeline endpoint instead:
```bash
# Use this instead of separate Stage A + B calls
curl -X POST 'http://localhost:8081/api/v1/generate-complete-pdf?project_id=ai' --output report.pdf
```

**Debug Commands:**
```bash
# Check Stage A output format
curl 'http://localhost:8081/api/v1/report-data?project_id=ai' | jq 'keys'

# Test Stage B with proper format (needs fixing)
curl 'http://localhost:8081/api/v1/report-data?project_id=ai' > temp.json
curl -X POST 'http://localhost:8081/api/v1/generate-insights' -H 'Content-Type: application/json' -d @temp.json
```

### Issue 4: High Token Usage

**Current Status:** ✅ **Optimized** - 60% reduction achieved

**Monitor Token Usage:**
```bash
# Check token usage trends
grep "tokens_used" logs/llm-calls.jsonl | jq '.tokens_used' | awk '{sum+=$1} END {print "Total tokens:", sum}'

# Check per-prompt usage
grep "business_summary" logs/llm-calls.jsonl | jq '.tokens_used'
```

**Token Optimization Tips:**
- ✅ Filter data to most relevant snippets (implemented)
- ✅ Use appropriate models for different tasks (implemented)
- ✅ Optimize prompt length (implemented)

## 📋 Debug Checklist

### Before Reporting Issues:

1. **Enable Debug Mode:**
   ```bash
   DEBUG=true go run cmd/combined_api/main.go
   ```

2. **Check Recent Logs:**
   ```bash
   tail -20 logs/llm-calls.jsonl
   ls -la logs/debug/
   ```

3. **Test Complete Pipeline:**
   ```bash
   curl -X POST 'http://localhost:8081/api/v1/generate-complete-pdf?project_id=ai' --output test.pdf
   ```

4. **Verify Environment:**
   ```bash
   echo $DEBUG
   echo $GCP_API_KEY | cut -c1-10  # Should show first 10 chars
   echo $GOOGLE_API_KEY | cut -c1-10
   ```

5. **Check System Resources:**
   ```bash
   df -h  # Disk space
   free -h  # Memory
   ```

## 🔧 Advanced Debugging

### Analyze LLM Call Patterns

```bash
# Count calls by provider
jq -r '.provider_alias' logs/llm-calls.jsonl | sort | uniq -c

# Average processing time by prompt
jq -r 'select(.event_type=="llm_response") | "\(.prompt_id) \(.processing_time)"' logs/llm-calls.jsonl

# Token usage by model
jq -r 'select(.tokens_used) | "\(.model_name) \(.tokens_used)"' logs/llm-calls.jsonl
```

### Debug Specific Components

```bash
# Test Stage A only
curl 'http://localhost:8081/api/v1/report-data?project_id=ai' | jq '.domain_scores | length'

# Check chart generation
curl -X POST 'http://localhost:8081/api/v1/generate-chart' \
  -H 'Content-Type: application/json' \
  -d '{"domains": ["A", "B"], "scores": [1, 2], "chart_type": "spider"}'

# Health checks
curl http://localhost:8081/health
curl http://localhost:8081/health-b
```

### Performance Analysis

```bash
# Processing time distribution
jq -r 'select(.processing_time) | .processing_time' logs/llm-calls.jsonl | \
  sed 's/[^0-9.]//g' | sort -n

# Token efficiency over time
jq -r 'select(.tokens_used) | "\(.timestamp) \(.tokens_used)"' logs/llm-calls.jsonl | \
  tail -10
```

## 📞 Getting Help

### Information to Include:

1. **Debug Logs:**
   - Last 10 entries from `logs/llm-calls.jsonl`
   - Console output with debug enabled
   - Any error messages

2. **Environment:**
   - Go version: `go version`
   - OS: `uname -a`
   - Available memory: `free -h`

3. **Request Details:**
   - Project ID used
   - Exact curl command or API call
   - Expected vs actual behavior

4. **Debug Artifacts:**
   - Latest files from `logs/debug/`
   - Specific timestamps of issues

### Quick Debug Commands:

```bash
# Generate debug report
echo "=== System Info ===" > debug_report.txt
go version >> debug_report.txt
echo "=== Recent LLM Calls ===" >> debug_report.txt
tail -5 logs/llm-calls.jsonl >> debug_report.txt
echo "=== Debug Artifacts ===" >> debug_report.txt
ls -la logs/debug/ >> debug_report.txt
```

## 🎯 Performance Targets

### Current Metrics (Optimized):
- **Token Usage**: ~107k tokens per complete report
- **Processing Time**: ~2m40s for complete pipeline
- **Success Rate**: 100% for complete PDF generation
- **Timeout Handling**: 10-minute LLM timeout

### Monitoring Commands:
```bash
# Real-time log monitoring
tail -f logs/llm-calls.jsonl | jq '.event_type, .processing_time, .tokens_used'

# Performance summary
echo "Recent performance:"
jq -r 'select(.event_type=="llm_response") | "\(.processing_time) \(.tokens_used)"' logs/llm-calls.jsonl | tail -5
```
